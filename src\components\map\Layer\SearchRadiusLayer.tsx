'use client'

import { useEffect, useState } from 'react';
import { useMap } from 'react-leaflet';
import { generateSearchRadiusData, type SearchRadiusData, SearchRadiusAPIData } from '@/lib/feature-data';
import {
	createMarkerClusterGroup,
	addMarkersToCluster,
	removeClusterGroupFromMap,
	addClusterGroupToMap,
	createColoredClusterIcon,
	addClusterEventListeners,
	type ClusterEventHandlers,
} from '@/lib/leaflet-clustering';

interface SearchRadiusLayerProps {
	data: SearchRadiusAPIData[];
	visible?: boolean;
	onDataChange?: (data: SearchRadiusData | null) => void;
}

export function SearchRadiusLayer({ data, visible = true, onDataChange }: SearchRadiusLayerProps) {
	const map = useMap();
	const [searchData, setSearchData] = useState<SearchRadiusData | null>(null);
	const [radius, setRadius] = useState(1000); // 預設 1000 公尺
	console.log('data', data);
	useEffect(() => {
		if (!map) return;

		let searchLayerGroup: any = null;
		let currentCircle: any = null;
		let L: any = null;
		let instructionControl: any = null;
		let isMounted = true;

		const initializeLeaflet = async () => {
			L = (await import('leaflet')).default;

			// 檢查組件是否仍然掛載
			if (!isMounted) return;

			searchLayerGroup = L.layerGroup();
		};

		const handleMapClick = async (e: any) => {
			const { lat, lng } = e.latlng;

			// 清除之前的圖層
			searchLayerGroup.clearLayers();

			// 創建搜索圓圈
			currentCircle = L.circle([lat, lng], {
				radius: radius,
				fillColor: '#8b5cf6',
				fillOpacity: 0.2,
				color: '#8b5cf6',
				weight: 2,
				opacity: 0.8,
			});

			// 添加中心點標記
			const centerIcon = L.divIcon({
				html: `
          <div style="
            background: #8b5cf6;
            color: white;
            padding: 4px 8px;
            border-radius: 50%;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            min-width: 24px;
            min-height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
          ">
            📍
          </div>
        `,
				className: 'search-center-marker',
				iconSize: [24, 24],
				iconAnchor: [12, 12],
			});

			const centerMarker = L.marker([lat, lng], { icon: centerIcon });
			centerMarker.bindPopup(`
        <div style="font-size: 14px;">
          <strong>搜索中心</strong><br/>
          <strong>座標:</strong> ${lat.toFixed(6)}, ${lng.toFixed(6)}<br/>
          <strong>搜索半徑:</strong> ${radius.toLocaleString()} 公尺
        </div>
      `);

			// 生成搜索範圍內的數據
			const data = generateSearchRadiusData(lat, lng, radius);
			console.log('data', data);
			setSearchData(data);
			onDataChange?.(data);

			// 創建 MarkerClusterGroup 來顯示搜索結果
			const clusterGroup = await createMarkerClusterGroup({
				maxClusterRadius: 60,
				disableClusteringAtZoom: 16,
				spiderfyOnMaxZoom: true,
				zoomToBoundsOnClick: true,
				showCoverageOnHover: false,
				iconCreateFunction: (cluster: any) => {
					const iconData = createColoredClusterIcon(cluster, '#8b5cf6', '搜索');
					return L.divIcon(iconData);
				},
			});

			// 添加聚合事件監聽器
			const eventHandlers: ClusterEventHandlers = {
				onClusterClick: (event: any) => {
					// 點擊聚合時放大到適當層級
					const cluster = event.layer || event.target;
					if (cluster && cluster.getAllChildMarkers) {
						const childMarkers = cluster.getAllChildMarkers();
						if (childMarkers.length > 0) {
							const group = L.featureGroup(childMarkers);
							map.fitBounds(group.getBounds(), {
								padding: [20, 20],
								maxZoom: 16,
							});
						}
					}
				},
			};

			addClusterEventListeners(clusterGroup, eventHandlers);

			// 創建標記函數
			const createMarkerFunction = (land: any, L: any) => {
				const landIcon = L.divIcon({
					html: `
            <div style="
              background: rgba(139, 92, 246, 0.8);
              color: white;
              padding: 2px 6px;
              border-radius: 4px;
              font-size: 10px;
              font-weight: bold;
              text-align: center;
              border: 1px solid white;
              box-shadow: 0 1px 3px rgba(0,0,0,0.2);
              white-space: nowrap;
            ">
              ${land.landNumber}
            </div>
          `,
					className: 'search-result-marker',
					iconSize: [50, 16] as [number, number],
					iconAnchor: [25, 8] as [number, number],
				});

				const landMarker = L.marker([land.lat, land.lng], { icon: landIcon });
				landMarker.bindPopup(`
          <div style="font-size: 14px;">
            <strong>搜索結果</strong><br/>
            <strong>地號:</strong> ${land.landNumber}<br/>
            <strong>地段:</strong> ${land.section}<br/>
            <strong>面積:</strong> ${land.area.toLocaleString()} 平方公尺<br/>
            <strong>年份:</strong> ${land.year}
          </div>
        `);

				return landMarker;
			};

			// 添加標記到聚合組
			await addMarkersToCluster(clusterGroup, data.lands, createMarkerFunction);

			// 添加聚合組到搜索圖層組
			searchLayerGroup.addLayer(clusterGroup);

			// 添加圓圈和中心點到圖層組
			searchLayerGroup.addLayer(currentCircle);
			searchLayerGroup.addLayer(centerMarker);

			// 添加到地圖
			searchLayerGroup.addTo(map);
		};

		const setupMapInteraction = async () => {
			await initializeLeaflet();

			// 檢查組件是否仍然掛載
			if (!isMounted) return null;

			// 只有當可見時才設置交互
			if (!visible) return null;

			// 監聽地圖點擊事件
			map.on('click', handleMapClick);

			// 添加提示
			instructionControl = L.control({ position: 'topleft' });
			instructionControl.onAdd = () => {
				const div = L.DomUtil.create('div', 'search-instruction');
				div.innerHTML = `
          <div style="
            background: rgba(139, 92, 246, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            margin: 10px;
          ">
            📍 點擊地圖設定搜索中心點<br/>
            半徑: ${radius.toLocaleString()}m
          </div>
        `;
				return div;
			};

			if (isMounted) {
				instructionControl.addTo(map);
			}

			return instructionControl;
		};

		setupMapInteraction();

		return () => {
			isMounted = false;
			map.off('click', handleMapClick);
			if (searchLayerGroup && map) {
				try {
					map.removeLayer(searchLayerGroup);
				} catch (error) {
					console.warn('Error removing search layer:', error);
				}
			}
			if (instructionControl && map) {
				try {
					map.removeControl(instructionControl);
				} catch (error) {
					console.warn('Error removing instruction control:', error);
				}
			}
			setSearchData(null);
			onDataChange?.(null);
		};
	}, [map, visible, radius]);

	return null;
}
