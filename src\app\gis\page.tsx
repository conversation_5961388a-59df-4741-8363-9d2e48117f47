'use client'

import { Map } from "lucide-react";
import { useState, useMemo } from 'react';
import dynamic from 'next/dynamic';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { TimelineSlider } from '@/components/map/timeline/TimelineSlider';
import { MapToolbar } from '@/components/map/MapTool/MapToolbar';
import { LayerPanel, type LayerConfig } from '@/components/map/Layer/LayerPanel';
import { MapMeasurementTool } from '@/components/map/MapTool/MapMeasurementTool';
// import { MapStatsPanel } from "@/components/map/MapStatsPanel";
import { HeatmapPanel } from '@/components/map/MapTool/HeatmapPanel';
import { HeatmapLayer, type HeatmapDataPoint } from '@/components/map/Layer/HeatmapLayer';
import { SurnameLayer } from '@/components/map/Layer/SurnameLayer';
import { LandLocationLayer } from '@/components/map/Layer/LandLocationLayer';
import { LandTypeLayer } from '@/components/map/Layer/LandTypeLayer';
import { SearchRadiusLayer } from '@/components/map/Layer/SearchRadiusLayer';
import { AnalysisFeature, FeatureSelector, type FeatureType } from '@/components/map/Feature/FeatureSelector';
import { FeatureStatus } from '@/components/map/Feature/FeatureStatus';
import { transformApiDataToHeatmap, fetchRealHeatmapData, filterApiDataByYearRange } from '@/lib/heatmap-api';
import { fetchGISData, getGISEndpoint, transformAPIData } from '@/lib/api';
import {
	generateMockSurnameData,
	generateMockLandLocationData,
	generateMockTransactionData,
	generateMockLandTypeData,
	// filterDataByTimeRange,
	type SurnameData,
	type LandLocationData,
	type TransactionData,
	type LandTypeData,
	SearchRadiusAPIData,
} from '@/lib/feature-data';
import { timelineConfig } from '@/config/map.config';
import { pickFirstInRangeById } from '@/lib/utils';

// 動態載入地圖組件以避免 SSR 問題
const LeafletMap = dynamic(() => import('@/components/map/LeafletMap'), {
	ssr: false,
	loading: () => (
		<div className="h-96 bg-muted flex items-center justify-center rounded-lg">
			<div className="text-center">
				<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
				<p className="text-muted-foreground">地圖載入中...</p>
			</div>
		</div>
	),
});

export default function GISPage() {
	const [map, setMap] = useState<any>(null);
	const [activeTool, setActiveTool] = useState<string | null>(null);
	const [showLayerPanel, setShowLayerPanel] = useState(false);
	const [showMeasurementTool, setShowMeasurementTool] = useState(false);
	const [showHeatmapPanel, setShowHeatmapPanel] = useState(false);
	const [heatmapVisible, setHeatmapVisible] = useState(false);
	const [heatmapOptions, setHeatmapOptions] = useState({});
	const [showFeatureSelector, setShowFeatureSelector] = useState(false);
	const [selectedFeature, setSelectedFeature] = useState<FeatureType>(null);
	const [timelineValues, setTimelineValues] = useState({
		startYear: timelineConfig.startYear,
		endYear: timelineConfig.endYear,
		currentYear: timelineConfig.currentYear,
	});

	// 模擬圖層數據
	const [layers, setLayers] = useState<LayerConfig[]>([
		{
			id: 'osm',
			name: 'OpenStreetMap',
			type: 'base',
			isVisible: true,
			description: '開放街圖底圖',
		},
		{
			id: 'satellite',
			name: '衛星影像',
			type: 'base',
			isVisible: false,
			description: '衛星影像底圖',
		},
		{
			id: 'century_map',
			name: '百年歷史地圖',
			type: 'overlay',
			isVisible: true,
			opacity: 0.7,
			description: '台灣百年歷史地圖疊加層',
		},
		{
			id: 'land_parcels',
			name: '土地地籍',
			type: 'overlay',
			isVisible: false,
			opacity: 0.8,
			description: '土地地籍界線',
		},
	]);

	// 統一的地圖數據狀態
	const [mapData, setMapData] = useState<any[]>([]);
	const [isLoadingMapData, setIsLoadingMapData] = useState(false);
	const [heatmapData, setHeatmapData] = useState<HeatmapDataPoint[]>([]);

	// 生成模擬熱力圖數據
	const generateMockHeatmapData = (): HeatmapDataPoint[] => {
		const data: HeatmapDataPoint[] = [];
		const centerLat = 22.5;
		const centerLng = 120.6;

		// 生成隨機數據點
		for (let i = 0; i < 100; i++) {
			const lat = centerLat + (Math.random() - 0.5) * 0.5;
			const lng = centerLng + (Math.random() - 0.5) * 0.5;
			const intensity = Math.random(); // 交易次數的歸一化值

			data.push({ lat, lng, intensity });
		}

		return data;
	};

	const handleMapReady = (mapInstance: any) => {
		setMap(mapInstance);
	};

	const handleTimelineChange = (values: { startYear: number; endYear: number; currentYear: number }) => {
		setTimelineValues(values);
		// 這裡可以根據時間軸的變化來更新地圖圖層
		// console.log('Timeline changed:', values);
	};

	const handleToolSelect = (tool: string) => {
		if (tool === 'feature-selector') {
			setShowFeatureSelector(!showFeatureSelector);
			setActiveTool(showFeatureSelector ? null : tool);
			setShowLayerPanel(false);
			setShowMeasurementTool(false);
			setShowHeatmapPanel(false);
		} else if (tool === 'layers') {
			setShowLayerPanel(!showLayerPanel);
			setActiveTool(showLayerPanel ? null : tool);
			setShowMeasurementTool(false);
			setShowHeatmapPanel(false);
			setShowFeatureSelector(false);
		} else if (tool === 'measure-distance' || tool === 'measure-area') {
			setShowMeasurementTool(!showMeasurementTool);
			setActiveTool(showMeasurementTool ? null : tool);
			setShowLayerPanel(false);
			setShowHeatmapPanel(false);
			setShowFeatureSelector(false);
		} else if (tool === 'heatmap') {
			setShowHeatmapPanel(!showHeatmapPanel);
			setActiveTool(showHeatmapPanel ? null : tool);
			setShowLayerPanel(false);
			setShowMeasurementTool(false);
			setShowFeatureSelector(false);
			// 如果是第一次打開熱力圖，生成模擬數據
			if (!showHeatmapPanel && heatmapData.length === 0) {
				setHeatmapData(generateMockHeatmapData());
			}
		} else {
			setActiveTool(activeTool === tool ? null : tool);
			setShowLayerPanel(false);
			setShowMeasurementTool(false);
			setShowHeatmapPanel(false);
			setShowFeatureSelector(false);
		}
	};

	const handleLayerToggle = (layerId: string, visible: boolean) => {
		setLayers((prev) => prev.map((layer) => (layer.id === layerId ? { ...layer, isVisible: visible } : layer)));
	};

	const handleOpacityChange = (layerId: string, opacity: number) => {
		setLayers((prev) => prev.map((layer) => (layer.id === layerId ? { ...layer, opacity } : layer)));
	};

	const handleHeatmapOptionsChange = (options: any) => {
		setHeatmapOptions(options);
	};

	const handleRefreshHeatmapData = async () => {
		try {
			// 使用 API 獲取數據
			// const apiResponse = await fetchHeatmapData({
			//   startYear: timelineValues.startYear,
			//   endYear: timelineValues.endYear,
			//   limit: 100
			// });

			const apiResponse = await fetchRealHeatmapData({
				startYear: timelineValues.startYear,
				endYear: timelineValues.endYear,
				// limit: 100
			});

			if (apiResponse?.data) {
				// const heatmapPoints = transformApiDataToHeatmap(apiResponse.data,
				//   timelineValues.startYear, timelineValues.endYear
				// );
				// 2) 依[startYear, currentYear] 裁切
				const sliced = filterApiDataByYearRange(apiResponse.data, timelineValues.startYear, timelineValues.currentYear);

				// 3) 依當前年 currentYear 計算強度
				const heat = transformApiDataToHeatmap(
					sliced,
					timelineValues.startYear,
					timelineValues.currentYear,
					timelineValues.endYear
				);

				setHeatmapData(heat);
				setHeatmapData(generateMockHeatmapData());
			} else {
				// 如果 API 失敗，使用本地模擬數據
				setHeatmapData(generateMockHeatmapData());
			}
		} catch (error) {
			console.error('獲取熱力圖數據失敗:', error);
			// 回退到本地模擬數據
			setHeatmapData(generateMockHeatmapData());
		}
	};

	const handleFeatureSelect = async (feature: FeatureType) => {
		// 清空所有功能的數據和選擇的功能，確保舊圖層被卸載
		setSelectedFeature(null);
		setMapData([]);
		setHeatmapVisible(false);
		setIsLoadingMapData(true);

		// 關閉功能選擇器
		setShowFeatureSelector(false);
		setActiveTool(null);

		// 使用 setTimeout 確保狀態更新完成後再設置新的功能
		setTimeout(async () => {
			try {
				setSelectedFeature(feature);

				// 根據選擇的功能載入對應的數據
				if (feature === 'surname-distribution') {
					const endpoint = getGISEndpoint(feature);
					const apiData = await fetchGISData(endpoint);
					// 轉換 API 數據格式，如果失敗則使用 mock 數據
					const transformedData = apiData?.data ? transformAPIData(apiData.data, feature) : [];
					const data =
						transformedData.length > 0
							? transformedData
							: generateMockSurnameData(timelineValues.startYear, timelineValues.endYear);
					const filteredData = pickFirstInRangeById(data, timelineValues.currentYear, timelineValues.endYear);
					setMapData(filteredData);
				} else if (feature === 'land-location') {
					const endpoint = getGISEndpoint(feature);
					const apiData = await fetchGISData(endpoint);
					const transformedData = apiData?.data ? transformAPIData(apiData.data, feature) : [];
					const data = transformedData.length > 0 ? transformedData : generateMockLandLocationData();
					setMapData(data);
				} else if (feature === 'transaction-heatmap') {
					const endpoint = getGISEndpoint(feature);
					const apiData = await fetchGISData(endpoint);
					setMapData(apiData?.data);
				} else if (feature === 'land-type-distribution') {
					const endpoint = getGISEndpoint(feature);
					const apiData = await fetchGISData(endpoint);
					const transformedData = apiData?.data ? transformAPIData(apiData.data, feature) : [];
					const data =
						transformedData.length > 0
							? transformedData
							: generateMockLandTypeData(timelineValues.startYear, timelineValues.endYear);
					const filteredData = pickFirstInRangeById(data, timelineValues.currentYear, timelineValues.endYear);
					setMapData(filteredData);
					// setMapData(data);
				} else if (feature === 'search-radius') {
					const endpoint = getGISEndpoint(feature);
					const apiData = await fetchGISData(endpoint);
					const transformedData = apiData?.data ? transformAPIData(apiData.data, feature) : [];
					const data =
						transformedData.length > 0
							? transformedData
							: generateMockLandTypeData(timelineValues.startYear, timelineValues.endYear);
					// const filteredData = pickFirstInRangeById(data, timelineValues.currentYear, timelineValues.endYear);
					setMapData(data);
				}
			} catch (error) {
				console.error('Error loading map data:', error);
				// 發生錯誤時使用 mock 數據
				if (feature === 'surname-distribution') {
					setMapData(generateMockSurnameData(timelineValues.startYear, timelineValues.endYear));
				} else if (feature === 'land-location') {
					setMapData(generateMockLandLocationData());
				} else if (feature === 'transaction-heatmap') {
					setMapData(generateMockTransactionData(timelineValues.startYear, timelineValues.endYear));
					setHeatmapVisible(true);
				} else if (feature === 'land-type-distribution') {
					setMapData(generateMockLandTypeData(timelineValues.startYear, timelineValues.endYear));
				} else {
					setMapData([]);
				}
			} finally {
				setIsLoadingMapData(false);
			}
		}, 0);
	};

	const enabledLayers = layers.filter((layer) => layer.isVisible).map((layer) => layer.id);

	const filteredMapData = useMemo(() => {
		const targets = new Set<AnalysisFeature>(['land-type-distribution', 'surname-distribution', 'transaction-heatmap']);
		let returnData;
		if (!mapData.length || !targets.has(selectedFeature as AnalysisFeature)) {
			return mapData;
		}
		if (selectedFeature === 'transaction-heatmap') {
			const sliced = filterApiDataByYearRange(mapData, timelineValues.startYear, timelineValues.currentYear);
			// 3) 依當前年 currentYear 計算強度
			const transformedHeatmapData = transformApiDataToHeatmap(
				sliced,
				timelineValues.startYear,
				timelineValues.currentYear,
				timelineValues.endYear
			);

			returnData = transformedHeatmapData;
			// 同時設置熱力圖可見
			setHeatmapVisible(true);
		} else {
			returnData = pickFirstInRangeById(mapData, timelineValues.currentYear, timelineValues.endYear);
		}

		return returnData;
	}, [
		mapData,
		selectedFeature,
		timelineValues.startYear,
		timelineValues.currentYear,
		timelineValues.endYear,
		filterApiDataByYearRange,
		transformApiDataToHeatmap,
	]);

	return (
		<div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
			<div className="container mx-auto px-4 py-8 space-y-6">
				<div className="max-w-7xl mx-auto">
					{/* Header */}
					<div className="mb-8 text-center md:text-left">
						<h1 className="text-4xl md:text-5xl font-bold mb-4 flex items-center justify-center md:justify-start">
							<Map className="w-10 h-10 mr-4 text-primary" />
							<span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
								GIS 地理資訊系統
							</span>
						</h1>
						<p className="text-lg md:text-xl text-muted-foreground max-w-3xl">提供互動式地圖服務。</p>
					</div>

					{/* Map Container */}
					<Card className="mb-6">
						<CardHeader className="pb-3">
							<div className="flex items-center justify-between">
								<CardTitle>互動式地圖</CardTitle>
								<div className="flex items-center space-x-3">
									{selectedFeature && (
										<FeatureStatus
											selectedFeature={selectedFeature}
											dataCount={isLoadingMapData ? 0 : mapData.length}
											isVisible={selectedFeature === 'transaction-heatmap' ? heatmapVisible : true}
											onToggleVisibility={
												selectedFeature === 'transaction-heatmap'
													? () => setHeatmapVisible(!heatmapVisible)
													: undefined
											}
											onClear={() => {
												setSelectedFeature(null);
												setMapData([]);
												setHeatmapVisible(false);
												setIsLoadingMapData(false);
											}}
										/>
									)}
									{isLoadingMapData && (
										<div className="flex items-center space-x-2 text-sm text-muted-foreground">
											<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
											<span>載入地圖數據中...</span>
										</div>
									)}
									<MapToolbar map={map} onToolSelect={handleToolSelect} activeTool={activeTool} />
								</div>
							</div>
						</CardHeader>
						<CardContent className="relative">
							<div className="relative">
								<LeafletMap height="500px" onMapReady={handleMapReady} enabledLayers={enabledLayers}>
									{/* 根據選擇的功能顯示對應的圖層 */}
									{selectedFeature === 'surname-distribution' && (
										<SurnameLayer
											// data={mapData as SurnameData[]}
											data={filteredMapData as SurnameData[]}
											visible={true}
										/>
									)}

									{selectedFeature === 'land-location' && (
										<LandLocationLayer data={mapData as LandLocationData[]} visible={true} />
									)}

									{selectedFeature === 'land-type-distribution' && (
										<LandTypeLayer
											// data={mapData as LandTypeData[]}
											data={filteredMapData as LandTypeData[]}
											visible={true}
										/>
									)}

									{selectedFeature === 'search-radius' && (
										<SearchRadiusLayer data={mapData as SearchRadiusAPIData[]} visible={true} />
									)}

									{selectedFeature === 'transaction-heatmap' && (
										<HeatmapLayer
											// data={mapData as TransactionData[]}
											// data={heatmapData}
											data={filteredMapData as TransactionData[]}
											isVisible={heatmapVisible}
											options={heatmapOptions}
										/>
									)}
								</LeafletMap>

								{/* Layer Panel Overlay */}
								{showLayerPanel && (
									<div className="absolute top-4 right-4 z-[1000]">
										<LayerPanel
											layers={layers}
											onLayerToggle={handleLayerToggle}
											onOpacityChange={handleOpacityChange}
										/>
									</div>
								)}

								{/* Measurement Tool Overlay */}
								{showMeasurementTool && (
									<div className="absolute top-4 left-4 z-[1000]">
										<MapMeasurementTool
											map={map}
											isActive={showMeasurementTool}
											onClose={() => {
												setShowMeasurementTool(false);
												setActiveTool(null);
											}}
										/>
									</div>
								)}

								{/* Heatmap Panel Overlay */}
								{showHeatmapPanel && (
									<div className="absolute top-4 right-4 z-[1000]">
										<HeatmapPanel
											isVisible={heatmapVisible}
											onVisibilityChange={setHeatmapVisible}
											onOptionsChange={handleHeatmapOptionsChange}
											onRefreshData={handleRefreshHeatmapData}
											dataCount={heatmapData.length}
											onClose={() => {
												setShowHeatmapPanel(false);
												setActiveTool(null);
											}}
										/>
									</div>
								)}

								{/* Feature Selector Overlay */}
								{showFeatureSelector && (
									<div className="absolute top-4 right-4 z-[1000]">
										<FeatureSelector
											selectedFeature={selectedFeature}
											onFeatureSelect={handleFeatureSelect}
											onClose={() => {
												setShowFeatureSelector(false);
												setActiveTool(null);
											}}
										/>
									</div>
								)}
							</div>
						</CardContent>
					</Card>

					{/* Map Statistics Panel */}
					{/* <MapStatsPanel
          map={map}
          timelineValues={timelineValues}
          enabledLayers={enabledLayers}
          className="mb-6"
        /> */}

					{/* Timeline Container */}
					<Card>
						<CardHeader>
							<CardTitle>時間軸控制</CardTitle>
						</CardHeader>
						<CardContent>
							<TimelineSlider
								startYear={timelineValues.startYear}
								endYear={timelineValues.endYear}
								currentYear={timelineValues.currentYear}
								minYear={timelineConfig.minYear}
								maxYear={timelineConfig.maxYear}
								onChange={handleTimelineChange}
							/>
						</CardContent>
					</Card>
				</div>
			</div>
		</div>
	);
}
