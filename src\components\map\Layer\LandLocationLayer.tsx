'use client';

import { useEffect, useState, useMemo } from 'react';
import { useMap } from 'react-leaflet';
import { LandLocationData } from '@/lib/feature-data';
import { filterDataByBounds, sampleDataByZoom, debounce, type MapBounds } from '@/lib/map-performance';
import { getMaxPointsForZoom, getViewportConfig, measurePerformance } from '@/config/map-performance.config';
import {
	createMarkerClusterGroup,
	addMarkersToCluster,
	removeClusterGroupFromMap,
	addClusterGroupToMap,
	createColoredClusterIcon,
	addClusterEventListeners,
	type ClusterEventHandlers,
} from '@/lib/leaflet-clustering';

interface LandLocationLayerProps {
	data: LandLocationData[];
	visible?: boolean;
}

export function LandLocationLayer({ data, visible = true }: LandLocationLayerProps) {
	const map = useMap();
	const [currentZoom, setCurrentZoom] = useState(10);
	const [mapBounds, setMapBounds] = useState<MapBounds | null>(null);

	// 使用 useMemo 優化數據處理
	const optimizedData = useMemo(() => {
		if (!data.length || !mapBounds) return data;

		// 1. 先根據視窗範圍過濾
		const boundsFiltered = filterDataByBounds(data, mapBounds, 0.2);

		// 2. 根據縮放層級進行採樣
		const maxPoints = currentZoom <= 12 ? 100 : currentZoom <= 15 ? 500 : 1000;
		const sampled = sampleDataByZoom(boundsFiltered, currentZoom, maxPoints);

		return sampled;
	}, [data, mapBounds, currentZoom]);

	// 防抖的邊界更新函數
	const debouncedUpdateBounds = useMemo(
		() =>
			debounce((bounds: MapBounds) => {
				setMapBounds(bounds);
			}, 300),
		[]
	);

	// 監聽縮放和移動變化
	useEffect(() => {
		if (!map) return;

		// 設置初始值
		setCurrentZoom(map.getZoom());
		const bounds = map.getBounds();
		setMapBounds({
			north: bounds.getNorth(),
			south: bounds.getSouth(),
			east: bounds.getEast(),
			west: bounds.getWest(),
		});

		// 監聽縮放變化
		const handleZoomEnd = () => {
			const newZoom = map.getZoom();
			setCurrentZoom(newZoom);

			const bounds = map.getBounds();
			debouncedUpdateBounds({
				north: bounds.getNorth(),
				south: bounds.getSouth(),
				east: bounds.getEast(),
				west: bounds.getWest(),
			});
		};

		// 監聽移動變化
		const handleMoveEnd = () => {
			const bounds = map.getBounds();
			debouncedUpdateBounds({
				north: bounds.getNorth(),
				south: bounds.getSouth(),
				east: bounds.getEast(),
				west: bounds.getWest(),
			});
		};

		map.on('zoomend', handleZoomEnd);
		map.on('moveend', handleMoveEnd);

		return () => {
			map.off('zoomend', handleZoomEnd);
			map.off('moveend', handleMoveEnd);
		};
	}, [map, debouncedUpdateBounds]);

	useEffect(() => {
		if (!map) return;

		let clusterGroup: any = null;
		let isMounted = true;

		const setupClusterLayer = async () => {
			try {
				// 檢查組件是否仍然掛載
				if (!isMounted) return;

				// 只有當可見且有數據時才創建標記
				if (visible && optimizedData.length > 0) {
					// 性能監控：只在開發環境下輸出
					if (process.env.NODE_ENV === 'development') {
						console.warn(`LandLocationLayer: 渲染 ${optimizedData.length}/${data.length} 個標記 (縮放: ${currentZoom})`);
					}

					// 動態導入 Leaflet
					const L = (await import('leaflet')).default;

					// 創建 MarkerClusterGroup
					clusterGroup = await createMarkerClusterGroup({
						maxClusterRadius: 80,
						disableClusteringAtZoom: 13, // 在 zoom 13 停止聚合，顯示地段層級
						spiderfyOnMaxZoom: true,
						zoomToBoundsOnClick: true,
						showCoverageOnHover: false,
						iconCreateFunction: (cluster: any) => {
							const childMarkers = cluster.getAllChildMarkers();
							const sections = new Set();
							childMarkers.forEach((marker: any) => {
								if (marker.options && marker.options.landData) {
									sections.add(marker.options.landData.section);
								}
							});

							// 如果縮放層級在 13-15 之間，顯示地段信息
							if (map.getZoom() >= 13 && map.getZoom() < 15) {
								const sectionList = Array.from(sections).slice(0, 2).join('、');
								const iconData = createColoredClusterIcon(cluster, '#22c55e', sectionList);
								return L.divIcon(iconData);
							} else {
								const iconData = createColoredClusterIcon(cluster, '#22c55e', '地段');
								return L.divIcon(iconData);
							}
						},
					});

					// 添加聚合事件監聽器
					const eventHandlers: ClusterEventHandlers = {
						onClusterClick: (event: any) => {
							// 點擊聚合時放大到適當層級
							const cluster = event.layer || event.target;
							if (cluster && cluster.getAllChildMarkers) {
								const childMarkers = cluster.getAllChildMarkers();
								if (childMarkers.length > 0) {
									const group = L.featureGroup(childMarkers);
									map.fitBounds(group.getBounds(), {
										padding: [20, 20],
										maxZoom: 15,
									});
								}
							}
						},
						onClusterMouseOver: (event: any) => {
							const cluster = event.layer || event.target;
							if (cluster && cluster.getAllChildMarkers) {
								const childMarkers = cluster.getAllChildMarkers();
								const sections = new Set();
								const landNumbers = [];

								childMarkers.forEach((marker: any) => {
									if (marker.options && marker.options.landData) {
										sections.add(marker.options.landData.section);
										landNumbers.push(marker.options.landData.landNumber);
									}
								});

								const sectionList = Array.from(sections).join('、');
								const landCount = landNumbers.length;

								const popup = L.popup({
									closeButton: false,
									autoClose: false,
									closeOnClick: false,
									className: 'cluster-hover-popup',
								})
									.setLatLng(cluster.getLatLng())
									.setContent(
										`
									<div style="font-size: 12px; padding: 4px;">
										<strong>地段:</strong> ${sectionList}<br/>
										<strong>地號數量:</strong> ${landCount}
									</div>
								`
									)
									.openOn(map);

								cluster._hoverPopup = popup;
							}
						},
						onClusterMouseOut: (event: any) => {
							const cluster = event.layer || event.target;
							if (cluster && cluster._hoverPopup) {
								map.closePopup(cluster._hoverPopup);
								cluster._hoverPopup = null;
							}
						},
					};

					addClusterEventListeners(clusterGroup, eventHandlers);

					// 創建標記函數
					const createMarkerFunction = (item: LandLocationData, L: any) => {
						const landIcon = L.divIcon({
							html: `
								<div style="
									background: rgba(16, 185, 129, 0.8);
									color: white;
									padding: 4px 8px;
									border-radius: 4px;
									font-size: 11px;
									font-weight: bold;
									text-align: center;
									border: 2px solid white;
									box-shadow: 0 2px 4px rgba(0,0,0,0.2);
									white-space: nowrap;
								">
									${item.landNumber}
								</div>
							`,
							className: 'land-number-marker',
							iconSize: [60, 20],
							iconAnchor: [30, 10],
						});

						const marker = L.marker([item.lat, item.lng], {
							icon: landIcon,
							landData: item, // 添加土地數據到標記選項中
						});

						marker.bindPopup(`
							<div style="font-size: 14px;">
								<strong>土地坐落</strong><br/>
								<strong>地號:</strong> ${item.landNumber}<br/>
								<strong>地段:</strong> ${item.section}<br/>
								<strong>年份:</strong> ${item.year}
							</div>
						`);

						return marker;
					};

					// 添加標記到聚合組
					await addMarkersToCluster(clusterGroup, optimizedData, createMarkerFunction);

					// 添加到地圖
					if (isMounted) {
						addClusterGroupToMap(map, clusterGroup);
					}
				}
			} catch (error) {
				console.error('Error setting up land location cluster layer:', error);
			}
		};

		setupClusterLayer();

		return () => {
			isMounted = false;
			if (clusterGroup) {
				removeClusterGroupFromMap(map, clusterGroup);
			}
		};
	}, [map, optimizedData, visible, currentZoom]);

	return null;
}
